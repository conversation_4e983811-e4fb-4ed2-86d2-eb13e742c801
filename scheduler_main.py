#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
定时执行邮件发送脚本
使用 schedule 库实现每天早上8:30自动执行
"""

import schedule
import time
import subprocess
import sys
import os
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scheduler.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def run_email_script():
    """执行邮件发送脚本"""
    try:
        logging.info("开始执行邮件发送脚本...")
        
        # 获取当前脚本所在目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        email_script_path = os.path.join(current_dir, 'send_email.py')
        
        # 检查脚本是否存在
        if not os.path.exists(email_script_path):
            logging.error(f"邮件脚本不存在: {email_script_path}")
            return False
        
        # 执行脚本
        result = subprocess.run(
            [sys.executable, email_script_path],
            capture_output=True,
            text=True,
            encoding='utf-8',
            cwd=current_dir
        )
        
        if result.returncode == 0:
            logging.info("邮件脚本执行成功")
            logging.info(f"输出: {result.stdout}")
            return True
        else:
            logging.error(f"邮件脚本执行失败，返回码: {result.returncode}")
            logging.error(f"错误输出: {result.stderr}")
            return False
            
    except Exception as e:
        logging.error(f"执行邮件脚本时发生异常: {e}")
        return False

def main():
    """主函数"""
    logging.info("定时任务调度器启动")
    logging.info("计划任务: 每天早上8:30执行邮件发送脚本")
    
    # 设置定时任务 - 每天早上8:30执行
    schedule.every().day.at("08:30").do(run_email_script)
    
    # 可选：添加测试任务（每分钟执行一次，用于测试）
    # schedule.every().minute.do(run_email_script)
    
    logging.info("定时任务已设置，等待执行...")
    
    try:
        while True:
            # 检查是否有待执行的任务
            schedule.run_pending()
            
            # 每30秒检查一次
            time.sleep(30)
            
    except KeyboardInterrupt:
        logging.info("收到中断信号，正在停止调度器...")
    except Exception as e:
        logging.error(f"调度器运行时发生异常: {e}")
    finally:
        logging.info("定时任务调度器已停止")

if __name__ == "__main__":
    main()
